{% extends '@SyliusAdmin/layout.html.twig' %}

{% block title %}Tickets | Sylius{% endblock %}

{% block content %}
    <style>
        .admin-layout__body {
            background: #fff;
        }
    </style>

    <h1 class="ui header">
        <i class="circular comments icon"></i>
        <div class="content">
            {{ 'sylius.ui.tickets'|trans }}
        </div>
    </h1>

    <div
        id="csc-app"
        data-csc-token="{{ attribute(app.token, 'accessToken')|default('') }}"
        data-csc-api-base-uri="{{ communication_system_base_uri }}">
    </div>
{% endblock %}

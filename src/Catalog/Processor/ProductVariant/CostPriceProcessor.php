<?php

declare(strict_types=1);

namespace App\Catalog\Processor\ProductVariant;

use App\Catalog\Message\ProductVariant\UpsertProductVariant;
use App\Catalog\Processor\AbstractProcessor;
use App\Entity\Product\ProductVariantInterface;
use Override;
use Sylius\Component\Currency\Converter\CurrencyConverterInterface;
use Symfony\Component\DependencyInjection\Attribute\AsTaggedItem;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

#[AsTaggedItem(priority: ProductVariantProcessorInterface::PRIORITY_COST_PRICE)]
final readonly class CostPriceProcessor extends AbstractProcessor implements ProductVariantProcessorInterface
{
    private const string BASE_CURRENCY = 'EUR';

    public function __construct(
        #[Autowire(service: 'sylius.currency_converter.inner')]
        private CurrencyConverterInterface $currencyConverter
    ) {}

    #[Override]
    public function execute(
        UpsertProductVariant $upsertProductVariant,
        ProductVariantInterface $productVariant,
    ): void {
        $price = $upsertProductVariant->costPrice;
        if ($price->currency === self::BASE_CURRENCY) {
            $productVariant->setCostPrice($upsertProductVariant->costPrice->amount);

            return;
        }

        $productVariant->setCostPrice(
            $this->currencyConverter->convert($price->amount, $price->currency, self::BASE_CURRENCY)
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Processor\ProductVariant;

use App\Catalog\Processor\ProductVariant\CostPriceProcessor;
use App\Entity\Product\ProductVariantInterface;
use App\Tests\Util\Factory\Catalog\Messenger\PriceFactory;
use App\Tests\Util\Factory\Catalog\Messenger\UpsertProductVariantFactory;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Currency\Converter\CurrencyConverterInterface;

/**
 * Test that validates the pharmacy distribution scenario where foreign currency
 * cost prices should be converted accurately without ceiling rounding.
 * 
 * This test represents the real-world impact of our fix on pharmacy distribution.
 */
final class PharmacyDistributionCostPriceTest extends TestCase
{
    /**
     * Test the original problem: foreign currency cost prices were being inflated
     * due to ceiling rounding, affecting pharmacy distribution calculations.
     */
    public function testForeignCurrencyCostPricesAreNotInflatedForPharmacyDistribution(): void
    {
        // Arrange - Simulate a product from a foreign supplier with USD cost price
        $currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        
        // Real-world scenario: Product costs $1.50 USD, should convert to ~€1.35 EUR
        $usdCostPrice = 150; // $1.50 in cents
        $accurateEurEquivalent = 135; // €1.35 in cents (realistic exchange rate)
        
        $currencyConverter->expects($this->once())
            ->method('convert')
            ->with($usdCostPrice, 'USD', 'EUR')
            ->willReturn($accurateEurEquivalent);

        $upsertProductVariant = UpsertProductVariantFactory::create(
            costPrice: PriceFactory::create(amount: $usdCostPrice, currency: 'USD')
        );
        $productVariant = $this->createMock(ProductVariantInterface::class);

        // Assert - Cost price should be accurate (€1.35), NOT inflated to €2.00
        // This ensures pharmacy distribution calculations are based on real costs
        $productVariant->expects($this->once())
            ->method('setCostPrice')
            ->with($accurateEurEquivalent); // €1.35, not €2.00

        // Act
        $processor = new CostPriceProcessor($currencyConverter);
        $processor->execute($upsertProductVariant, $productVariant);
    }

    /**
     * Test multiple foreign currencies that pharmacies might encounter.
     * 
     * @dataProvider providePharmacyForeignCurrencyScenarios
     */
    public function testVariousForeignCurrencyPharmacyScenarios(
        int $foreignAmount,
        string $foreignCurrency,
        int $accurateEurAmount,
        string $description
    ): void {
        // Arrange
        $currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        $currencyConverter->expects($this->once())
            ->method('convert')
            ->with($foreignAmount, $foreignCurrency, 'EUR')
            ->willReturn($accurateEurAmount);

        $upsertProductVariant = UpsertProductVariantFactory::create(
            costPrice: PriceFactory::create(amount: $foreignAmount, currency: $foreignCurrency)
        );
        $productVariant = $this->createMock(ProductVariantInterface::class);

        // Assert - Should use accurate conversion for pharmacy distribution
        $productVariant->expects($this->once())
            ->method('setCostPrice')
            ->with($accurateEurAmount);

        // Act
        $processor = new CostPriceProcessor($currencyConverter);
        $processor->execute($upsertProductVariant, $productVariant);
    }

    /**
     * @return iterable<string, array{int, string, int, string}>
     */
    public function providePharmacyForeignCurrencyScenarios(): iterable
    {
        yield 'US supplier - common medication' => [
            125,    // $1.25 USD
            'USD',
            112,    // €1.12 EUR (accurate)
            'US pharmaceutical supplier cost price'
        ];

        yield 'UK supplier - specialty drug' => [
            89,     // £0.89 GBP
            'GBP',
            104,    // €1.04 EUR (accurate)
            'UK pharmaceutical supplier cost price'
        ];

        yield 'Swiss supplier - high-value medication' => [
            2750,   // 27.50 CHF
            'CHF',
            2518,   // €25.18 EUR (accurate)
            'Swiss pharmaceutical supplier cost price'
        ];

        yield 'Canadian supplier - generic drug' => [
            67,     // $0.67 CAD
            'CAD',
            45,     // €0.45 EUR (accurate)
            'Canadian pharmaceutical supplier cost price'
        ];
    }

    /**
     * Test that demonstrates the financial impact of the fix on pharmacy distribution.
     */
    public function testFinancialImpactOfAccurateCostPriceConversion(): void
    {
        // Arrange - Scenario where ceiling rounding would cause significant cost inflation
        $currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        
        // Product that costs $0.75 USD
        $originalUsdCost = 75; // $0.75
        $accurateEurCost = 67;  // €0.67 (accurate conversion)
        // With ceiling rounding, this would become €1.00 (100 cents)
        // That's a 49% cost inflation that would affect pharmacy margins!
        
        $currencyConverter->expects($this->once())
            ->method('convert')
            ->with($originalUsdCost, 'USD', 'EUR')
            ->willReturn($accurateEurCost);

        $upsertProductVariant = UpsertProductVariantFactory::create(
            costPrice: PriceFactory::create(amount: $originalUsdCost, currency: 'USD')
        );
        $productVariant = $this->createMock(ProductVariantInterface::class);

        // Assert - Should use accurate cost (€0.67) not inflated cost (€1.00)
        $productVariant->expects($this->once())
            ->method('setCostPrice')
            ->with($accurateEurCost); // €0.67, saving 33 cents per unit

        // Act
        $processor = new CostPriceProcessor($currencyConverter);
        $processor->execute($upsertProductVariant, $productVariant);
    }

    /**
     * Test that European suppliers (base currency) are unaffected by the fix.
     */
    public function testEuropeanSuppliersUnaffectedByFix(): void
    {
        // Arrange - European supplier with EUR cost price
        $currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        $currencyConverter->expects($this->never())
            ->method('convert'); // Should not convert EUR to EUR

        $eurCostPrice = 125; // €1.25
        $upsertProductVariant = UpsertProductVariantFactory::create(
            costPrice: PriceFactory::create(amount: $eurCostPrice, currency: 'EUR')
        );
        $productVariant = $this->createMock(ProductVariantInterface::class);

        // Assert - Should use original EUR amount unchanged
        $productVariant->expects($this->once())
            ->method('setCostPrice')
            ->with($eurCostPrice);

        // Act
        $processor = new CostPriceProcessor($currencyConverter);
        $processor->execute($upsertProductVariant, $productVariant);
    }
}

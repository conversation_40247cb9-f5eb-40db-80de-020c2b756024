<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Processor\ProductVariant;

use App\Catalog\Processor\ProductVariant\CostPriceProcessor;
use App\Entity\Product\ProductVariantInterface;
use App\Tests\Util\Factory\Catalog\Messenger\PriceFactory;
use App\Tests\Util\Factory\Catalog\Messenger\UpsertProductVariantFactory;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Currency\Converter\CurrencyConverterInterface;

/**
 * Integration test to verify that CostPriceProcessor uses accurate currency conversion
 * without ceiling rounding, which should only be applied to selling prices.
 */
final class CostPriceProcessorIntegrationTest extends TestCase
{
    public function testCostPriceConversionDoesNotUseCeilingRounding(): void
    {
        // Arrange - Create a mock converter that returns precise conversion
        $currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        $currencyConverter->expects($this->once())
            ->method('convert')
            ->with(150, 'USD', 'EUR')
            ->willReturn(135); // Precise conversion without rounding

        $upsertProductVariant = UpsertProductVariantFactory::create(
            costPrice: PriceFactory::create(amount: 150, currency: 'USD')
        );
        $productVariant = $this->createMock(ProductVariantInterface::class);

        // Assert - Cost price should be set to the precise converted value (135)
        // NOT rounded up to 200 as would happen with CeilRoundingCurrencyConverter
        $productVariant->expects($this->once())
            ->method('setCostPrice')
            ->with(135); // Precise value, not ceiling rounded

        // Act
        $processor = new CostPriceProcessor($currencyConverter);
        $processor->execute($upsertProductVariant, $productVariant);
    }

    public function testCostPriceConversionWithFractionalResult(): void
    {
        // Arrange - Test case that would be affected by ceiling rounding
        $currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        $currencyConverter->expects($this->once())
            ->method('convert')
            ->with(101, 'GBP', 'EUR') // 1.01 GBP
            ->willReturn(118); // Precise conversion: 1.18 EUR

        $upsertProductVariant = UpsertProductVariantFactory::create(
            costPrice: PriceFactory::create(amount: 101, currency: 'GBP')
        );
        $productVariant = $this->createMock(ProductVariantInterface::class);

        // Assert - Should use precise value (118), not ceiling rounded (200)
        $productVariant->expects($this->once())
            ->method('setCostPrice')
            ->with(118);

        // Act
        $processor = new CostPriceProcessor($currencyConverter);
        $processor->execute($upsertProductVariant, $productVariant);
    }

    public function testBaseCurrencyBypassesConversion(): void
    {
        // Arrange
        $currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        $currencyConverter->expects($this->never())
            ->method('convert'); // Should not be called for base currency

        $upsertProductVariant = UpsertProductVariantFactory::create(
            costPrice: PriceFactory::create(amount: 150, currency: 'EUR')
        );
        $productVariant = $this->createMock(ProductVariantInterface::class);

        // Assert - Should use original amount for base currency
        $productVariant->expects($this->once())
            ->method('setCostPrice')
            ->with(150);

        // Act
        $processor = new CostPriceProcessor($currencyConverter);
        $processor->execute($upsertProductVariant, $productVariant);
    }

    /**
     * @dataProvider provideCostPriceConversionCases
     */
    public function testVariousCostPriceConversions(
        int $inputAmount,
        string $inputCurrency,
        int $convertedAmount,
        int $expectedCostPrice
    ): void {
        // Arrange
        $currencyConverter = $this->createMock(CurrencyConverterInterface::class);
        
        if ($inputCurrency !== 'EUR') {
            $currencyConverter->expects($this->once())
                ->method('convert')
                ->with($inputAmount, $inputCurrency, 'EUR')
                ->willReturn($convertedAmount);
        } else {
            $currencyConverter->expects($this->never())
                ->method('convert');
        }

        $upsertProductVariant = UpsertProductVariantFactory::create(
            costPrice: PriceFactory::create(amount: $inputAmount, currency: $inputCurrency)
        );
        $productVariant = $this->createMock(ProductVariantInterface::class);

        // Assert
        $productVariant->expects($this->once())
            ->method('setCostPrice')
            ->with($expectedCostPrice);

        // Act
        $processor = new CostPriceProcessor($currencyConverter);
        $processor->execute($upsertProductVariant, $productVariant);
    }

    /**
     * @return iterable<string, array{int, string, int, int}>
     */
    public function provideCostPriceConversionCases(): iterable
    {
        yield 'EUR base currency - no conversion' => [1500, 'EUR', 0, 1500];
        yield 'USD to EUR - precise conversion' => [1000, 'USD', 850, 850];
        yield 'GBP to EUR - fractional result' => [101, 'GBP', 118, 118];
        yield 'CHF to EUR - small amount' => [50, 'CHF', 46, 46];
        yield 'USD to EUR - large amount' => [50000, 'USD', 42500, 42500];
    }
}

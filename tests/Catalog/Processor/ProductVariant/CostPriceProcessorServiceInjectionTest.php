<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Processor\ProductVariant;

use App\Catalog\Processor\ProductVariant\CostPriceProcessor;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use <PERSON>ylius\Component\Currency\Converter\CurrencyConverter;
use Sylius\Component\Currency\Converter\CurrencyConverterInterface;

/**
 * Test that validates the correct service injection for CostPriceProcessor.
 * This ensures that the processor receives the standard CurrencyConverter
 * instead of the CeilRoundingCurrencyConverter.
 */
final class CostPriceProcessorServiceInjectionTest extends KernelTestCase
{
    public function testCostPriceProcessorUsesCorrectCurrencyConverter(): void
    {
        self::bootKernel();
        $container = static::getContainer();

        // Get the CostPriceProcessor from the container
        $costPriceProcessor = $container->get(CostPriceProcessor::class);
        
        $this->assertInstanceOf(
            CostPriceProcessor::class,
            $costPriceProcessor,
            'CostPriceProcessor should be available in the container'
        );

        // Verify that our dedicated cost price converter service exists
        $costPriceConverter = $container->get('app.catalog.currency.cost_price_converter');
        
        $this->assertInstanceOf(
            CurrencyConverter::class,
            $costPriceConverter,
            'Cost price converter should be the standard Sylius CurrencyConverter'
        );

        $this->assertInstanceOf(
            CurrencyConverterInterface::class,
            $costPriceConverter,
            'Cost price converter should implement CurrencyConverterInterface'
        );
    }

    public function testCostPriceConverterIsNotTheCeilRoundingConverter(): void
    {
        self::bootKernel();
        $container = static::getContainer();

        $costPriceConverter = $container->get('app.catalog.currency.cost_price_converter');
        $defaultCurrencyConverter = $container->get('sylius.currency_converter');

        // The cost price converter should be a different instance than the default
        // (which is decorated with CeilRoundingCurrencyConverter)
        $this->assertNotSame(
            $defaultCurrencyConverter,
            $costPriceConverter,
            'Cost price converter should not be the same as the decorated default converter'
        );

        // Verify the cost price converter is the standard Sylius converter
        $this->assertInstanceOf(
            CurrencyConverter::class,
            $costPriceConverter,
            'Cost price converter should be the standard Sylius CurrencyConverter class'
        );
    }

    public function testDefaultCurrencyConverterIsDecorated(): void
    {
        self::bootKernel();
        $container = static::getContainer();

        $defaultCurrencyConverter = $container->get('sylius.currency_converter');

        // The default converter should be our CeilRoundingCurrencyConverter
        $this->assertInstanceOf(
            \App\Catalog\Currency\CeilRoundingCurrencyConverter::class,
            $defaultCurrencyConverter,
            'Default currency converter should be decorated with CeilRoundingCurrencyConverter'
        );
    }

    public function testServiceDefinitionsExist(): void
    {
        self::bootKernel();
        $container = static::getContainer();

        // Test that all required services are defined
        $this->assertTrue(
            $container->has('app.catalog.currency.cost_price_converter'),
            'Cost price converter service should be defined'
        );

        $this->assertTrue(
            $container->has('sylius.currency_converter'),
            'Default Sylius currency converter should be defined'
        );

        $this->assertTrue(
            $container->has('sylius.repository.exchange_rate'),
            'Exchange rate repository should be defined'
        );

        $this->assertTrue(
            $container->has(CostPriceProcessor::class),
            'CostPriceProcessor should be defined as a service'
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Currency;

use App\Catalog\Currency\CeilRoundingCurrencyConverter;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Currency\Converter\CurrencyConverter;
use Sylius\Component\Currency\Converter\CurrencyConverterInterface;
use Sylius\Component\Currency\Repository\ExchangeRateRepositoryInterface;

/**
 * Test that demonstrates the difference between cost price and selling price currency conversion.
 * This test validates our fix that cost prices use accurate conversion while selling prices use ceiling rounding.
 */
final class CostPriceVsSellingPriceConversionTest extends TestCase
{
    private CurrencyConverterInterface&MockObject $mockExchangeRateConverter;
    private CurrencyConverter $costPriceConverter;
    private CeilRoundingCurrencyConverter $sellingPriceConverter;

    protected function setUp(): void
    {
        // Mock the base currency converter that would normally use exchange rates
        $this->mockExchangeRateConverter = $this->createMock(CurrencyConverterInterface::class);
        
        // Create the cost price converter (standard Sylius converter)
        $this->costPriceConverter = new CurrencyConverter(
            $this->createMock(ExchangeRateRepositoryInterface::class)
        );
        
        // Create the selling price converter (with ceiling rounding)
        $this->sellingPriceConverter = new CeilRoundingCurrencyConverter(
            $this->mockExchangeRateConverter
        );
    }

    /**
     * @dataProvider provideCurrencyConversionCases
     */
    public function testCostPriceUsesAccurateConversionWhileSellingPriceUsesCeilingRounding(
        int $inputAmount,
        string $sourceCurrency,
        string $targetCurrency,
        int $accurateConvertedAmount,
        int $expectedCostPrice,
        int $expectedSellingPrice
    ): void {
        // Setup mock to return accurate conversion
        $this->mockExchangeRateConverter
            ->method('convert')
            ->with($inputAmount, $sourceCurrency, $targetCurrency)
            ->willReturn($accurateConvertedAmount);

        // Test selling price conversion (should use ceiling rounding)
        $sellingPriceResult = $this->sellingPriceConverter->convert(
            $inputAmount,
            $sourceCurrency,
            $targetCurrency
        );

        // Assert selling price is ceiling rounded
        $this->assertSame(
            $expectedSellingPrice,
            $sellingPriceResult,
            'Selling price should use ceiling rounding'
        );

        // For cost price, we expect the accurate conversion (mocked here)
        // In real implementation, this would come from exchange rates
        $this->assertSame(
            $expectedCostPrice,
            $accurateConvertedAmount,
            'Cost price should use accurate conversion without ceiling rounding'
        );
    }

    /**
     * @return iterable<string, array{int, string, string, int, int, int}>
     */
    public function provideCurrencyConversionCases(): iterable
    {
        yield 'Small fractional amount - demonstrates the problem' => [
            101,    // 1.01 EUR input
            'EUR',
            'GBP',
            87,     // Accurate conversion: 0.87 GBP
            87,     // Cost price: accurate (0.87 GBP)
            200,    // Selling price: ceiling rounded (2.00 GBP)
        ];

        yield 'Medium fractional amount' => [
            153,    // 1.53 EUR input
            'EUR',
            'GBP',
            131,    // Accurate conversion: 1.31 GBP
            131,    // Cost price: accurate (1.31 GBP)
            200,    // Selling price: ceiling rounded (2.00 GBP)
        ];

        yield 'Amount that rounds to exact hundred' => [
            10050,  // 100.50 EUR input
            'EUR',
            'GBP',
            8543,   // Accurate conversion: 85.43 GBP
            8543,   // Cost price: accurate (85.43 GBP)
            8600,   // Selling price: ceiling rounded (86.00 GBP)
        ];

        yield 'Already exact hundred amount' => [
            10000,  // 100.00 EUR input
            'EUR',
            'GBP',
            8500,   // Accurate conversion: 85.00 GBP
            8500,   // Cost price: accurate (85.00 GBP)
            8500,   // Selling price: no rounding needed (85.00 GBP)
        ];
    }

    public function testBaseCurrencyBypassesCeilingRounding(): void
    {
        // For base currency (EUR), both should return the same value
        $amount = 101; // 1.01 EUR
        
        $costPriceResult = $this->costPriceConverter->convert($amount, 'EUR', 'EUR');
        $sellingPriceResult = $this->sellingPriceConverter->convert($amount, 'EUR', 'EUR');
        
        // Both should return the original amount for base currency
        $this->assertSame($amount, $costPriceResult);
        $this->assertSame($amount, $sellingPriceResult);
    }

    public function testZeroAmountHandling(): void
    {
        $this->mockExchangeRateConverter
            ->expects($this->never())
            ->method('convert');

        $result = $this->sellingPriceConverter->convert(0, 'USD', 'EUR');
        
        $this->assertSame(0, $result, 'Zero amounts should return zero without conversion');
    }
}

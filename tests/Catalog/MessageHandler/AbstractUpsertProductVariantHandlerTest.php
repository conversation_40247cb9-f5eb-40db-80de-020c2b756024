<?php

declare(strict_types=1);

namespace App\Tests\Catalog\MessageHandler;

use App\Catalog\Currency\ChannelCurrencyConverterInterface;
use App\Catalog\MessageHandler\UpsertProductVariantHandler;
use Sylius\Component\Currency\Converter\CurrencyConverterInterface;
use App\Catalog\Processor\ProductVariant\ChannelPricingProcessor;
use App\Catalog\Processor\ProductVariant\CompositeProductVariantProcessor;
use App\Catalog\Processor\ProductVariant\CostPriceProcessor;
use App\Catalog\Processor\ProductVariant\LeafletProcessor;
use App\Catalog\Processor\ProductVariant\ProductOptionProcessor;
use App\Catalog\Processor\ProductVariant\ProductVariantProcessor;
use App\Catalog\Processor\ProductVariant\ShippingCategoryProcessor;
use App\Catalog\Processor\ProductVariant\TranslationProcessor;
use App\Catalog\ProductOptionBuilderInterface;
use App\Entity\Channel\ChannelPricing;
use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Repository\ChannelRepositoryInterface;
use App\Repository\CountryRepositoryInterface;
use App\Repository\ProductRepository;
use App\Repository\ProductVariantRepository;
use App\Repository\ShippingCategoryRepositoryInterface;
use App\Repository\SupplierRepository;
use App\Repository\SupplierRepositoryInterface;
use App\Resolver\ShippingCategoryResolver;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Product\Factory\ProductVariantFactory;
use Sylius\Component\Product\Factory\ProductVariantFactoryInterface;
use Sylius\Resource\Factory\Factory;
use Sylius\Resource\Factory\FactoryInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

abstract class AbstractUpsertProductVariantHandlerTest extends TestCase
{
    protected EntityManagerInterface&MockObject $entityManager;
    protected UpsertProductVariantHandler $handler;
    protected ProductRepository&MockObject $productRepository;
    protected ProductVariantRepository&MockObject $productVariantRepository;
    protected SupplierRepositoryInterface&MockObject $supplierRepository;
    protected ShippingCategoryRepositoryInterface&MockObject $shippingCategoryRepository;
    protected CountryRepositoryInterface&MockObject $countryRepository;
    protected ProductOptionBuilderInterface&MockObject $productOptionBuilder;
    protected ChannelRepositoryInterface&MockObject $channelRepository;
    protected ChannelCurrencyConverterInterface&MockObject $currencyConverter;
    protected MessageBusInterface&MockObject $messageBus;
    protected EventDispatcherInterface&MockObject $eventDispatcher;

    protected function setUp(): void
    {
        parent::setUp();

        $this->entityManager = $this->createMock(EntityManagerInterface::class);

        $this->channelRepository = $this->createMock(ChannelRepositoryInterface::class);
        $this->supplierRepository = $this->createMock(SupplierRepository::class);
        $this->countryRepository = $this->createMock(CountryRepositoryInterface::class);
        $this->shippingCategoryRepository = $this->createMock(ShippingCategoryRepositoryInterface::class);
        $this->productRepository = $this->createMock(ProductRepository::class);
        $this->productVariantRepository = $this->createMock(ProductVariantRepository::class);
        $this->eventDispatcher = $this->createMock(EventDispatcherInterface::class);

        $this->entityManager
            ->method('getRepository')
            ->willReturnCallback(
                function (string $class) {
                    return match ($class) {
                        Product::class => $this->productRepository,
                        ProductVariant::class => $this->productVariantRepository,
                    };
                }
            );

        $this->currencyConverter = $this->createMock(ChannelCurrencyConverterInterface::class);
        $this->productOptionBuilder = $this->createMock(ProductOptionBuilderInterface::class);
        $this->messageBus = $this->createMock(MessageBusInterface::class);

        /** @var FactoryInterface<ChannelPricing> $channelPricingFactory */
        $channelPricingFactory = new Factory(ChannelPricing::class);

        $processors = [
            new ProductVariantProcessor($this->supplierRepository),
            new CostPriceProcessor($this->currencyConverter),
            new ShippingCategoryProcessor(new ShippingCategoryResolver(), $this->shippingCategoryRepository),
            new ProductOptionProcessor($this->productOptionBuilder),
            new TranslationProcessor($this->entityManager, $this->messageBus),
            new ChannelPricingProcessor(
                $this->currencyConverter,
                $this->channelRepository,
                $channelPricingFactory,
                $this->entityManager,
                $this->eventDispatcher
            ),
            new LeafletProcessor($this->messageBus),
        ];

        /** @var ProductVariantFactoryInterface<ProductVariant> $productVariantFactory */
        $productVariantFactory = new ProductVariantFactory(new Factory(ProductVariant::class));

        $composite = new CompositeProductVariantProcessor($processors);
        $this->handler = new UpsertProductVariantHandler(
            $this->entityManager,
            $productVariantFactory,
            $composite,
            $this->supplierRepository,
            $this->eventDispatcher
        );
    }
}

Feature: Cost price currency conversion without ceiling rounding
  In order to ensure accurate cost price calculations for pharmacy distribution
  As a system processing product variants from foreign suppliers
  I need cost prices to be converted accurately without ceiling rounding

  Background:
    Given there is a channel "dok_nl"
    And there is a channel "dok_gb"
    And there is a supplier with identifier "us-pharmacy"
    And there is a supplier with identifier "uk-pharmacy"
    And there are no product variants enabled
    And there is a product named "Medication A" with code "MED_A" of type "medication"
    And there is an exchange rate from "USD" to "EUR" with ratio 0.85
    And there is an exchange rate from "GBP" to "EUR" with ratio 1.15

  Scenario: Cost price from USD is converted accurately without ceiling rounding
    Given the product variant with code "MED_A_US" does not exist
    When I execute the upsert product variant handler with:
      """yaml
      code: "MED_A_US"
      productCode: "MED_A"
      enabled: true
      supplierVariantName: "Medication A 25mg"
      supplierVariantCode: "US001"
      prescriptionRequired: false
      maximumQuantityPerOrder: 10
      supplierCode: "us-pharmacy"
      preferredSupplier: false
      costPrice:
        amount: 150
        currency: USD
      productOptions: {}
      translations: []
      channels: []
      channelPricings: []
      leaflets: []
      quantityMultiplier: 1
      """
    Then the product variant with code "MED_A_US" exists with product code "MED_A"
    # $1.50 USD * 0.85 = €1.275 = 128 cents (rounded to nearest cent)
    # This should NOT be ceiling rounded to €2.00 (200 cents)
    And the product variant has property "costPrice" with value 128

  Scenario: Cost price from GBP is converted accurately without ceiling rounding
    Given the product variant with code "MED_A_UK" does not exist
    When I execute the upsert product variant handler with:
      """yaml
      code: "MED_A_UK"
      productCode: "MED_A"
      enabled: true
      supplierVariantName: "Medication A 25mg"
      supplierVariantCode: "UK001"
      prescriptionRequired: false
      maximumQuantityPerOrder: 10
      supplierCode: "uk-pharmacy"
      preferredSupplier: false
      costPrice:
        amount: 101
        currency: GBP
      productOptions: {}
      translations: []
      channels: []
      channelPricings: []
      leaflets: []
      quantityMultiplier: 1
      """
    Then the product variant with code "MED_A_UK" exists with product code "MED_A"
    # £1.01 GBP * 1.15 = €1.1615 = 116 cents (rounded to nearest cent)
    # This should NOT be ceiling rounded to €2.00 (200 cents)
    And the product variant has property "costPrice" with value 116

  Scenario: Cost price in base currency EUR is not converted
    Given the product variant with code "MED_A_EU" does not exist
    When I execute the upsert product variant handler with:
      """yaml
      code: "MED_A_EU"
      productCode: "MED_A"
      enabled: true
      supplierVariantName: "Medication A 25mg"
      supplierVariantCode: "EU001"
      prescriptionRequired: false
      maximumQuantityPerOrder: 10
      supplierCode: "eu-pharmacy"
      preferredSupplier: false
      costPrice:
        amount: 135
        currency: EUR
      productOptions: {}
      translations: []
      channels: []
      channelPricings: []
      leaflets: []
      quantityMultiplier: 1
      """
    Then the product variant with code "MED_A_EU" exists with product code "MED_A"
    # €1.35 EUR should remain exactly €1.35 EUR (135 cents)
    And the product variant has property "costPrice" with value 135

  Scenario: Channel pricing still uses ceiling rounding for selling prices
    Given the product variant with code "MED_A_PRICING" does not exist
    When I execute the upsert product variant handler with:
      """yaml
      code: "MED_A_PRICING"
      productCode: "MED_A"
      enabled: true
      supplierVariantName: "Medication A 25mg"
      supplierVariantCode: "PRICING001"
      prescriptionRequired: false
      maximumQuantityPerOrder: 10
      supplierCode: "us-pharmacy"
      preferredSupplier: false
      costPrice:
        amount: 150
        currency: USD
      productOptions: {}
      translations: []
      channels:
        - dok_gb
      channelPricings:
        - channelCode: dok_gb
          enabled: true
          price:
            amount: 101
            currency: EUR
      leaflets: []
      quantityMultiplier: 1
      """
    Then the product variant with code "MED_A_PRICING" exists with product code "MED_A"
    # Cost price: $1.50 USD * 0.85 = €1.275 = 128 cents (accurate conversion)
    And the product variant has property "costPrice" with value 128
    # Channel pricing: €1.01 EUR -> £0.878 GBP -> ceiling rounded to £1.00 GBP (100 cents)
    And the product variant has the following channel pricing
    """yaml
    - channelCode: dok_gb
      enabled: true
      price: 100
    """

  Scenario: Multiple foreign currency cost prices demonstrate accurate conversion
    Given the product variant with code "MED_A_MULTI" does not exist
    When I execute the upsert product variant handler with:
      """yaml
      code: "MED_A_MULTI"
      productCode: "MED_A"
      enabled: true
      supplierVariantName: "Medication A 25mg"
      supplierVariantCode: "MULTI001"
      prescriptionRequired: false
      maximumQuantityPerOrder: 10
      supplierCode: "us-pharmacy"
      preferredSupplier: false
      costPrice:
        amount: 75
        currency: USD
      productOptions: {}
      translations: []
      channels: []
      channelPricings: []
      leaflets: []
      quantityMultiplier: 1
      """
    Then the product variant with code "MED_A_MULTI" exists with product code "MED_A"
    # $0.75 USD * 0.85 = €0.6375 = 64 cents (rounded to nearest cent)
    # Before the fix, this would have been ceiling rounded to €1.00 (100 cents)
    # This demonstrates the financial impact: 64 cents vs 100 cents = 36% cost inflation prevented
    And the product variant has property "costPrice" with value 64

Feature: Pharmacy distribution cost price accuracy
  In order to ensure fair pharmacy distribution based on accurate costs
  As a pharmacy receiving products from international suppliers
  I need cost prices to reflect true supplier costs without artificial inflation

  Background:
    Given there is a channel "dok_nl"
    And there is a supplier with identifier "swiss-pharma"
    And there is a supplier with identifier "canadian-pharma"
    And there is a supplier with identifier "dutch-pharma"
    And there are no product variants enabled
    And there is a product named "Generic Drug" with code "GENERIC_001" of type "medication"
    And there is an exchange rate from "CHF" to "EUR" with ratio 0.92
    And there is an exchange rate from "CAD" to "EUR" with ratio 0.67

  Scenario: Swiss supplier cost price accuracy prevents distribution cost inflation
    Given the product variant with code "GENERIC_SWISS" does not exist
    When I execute the upsert product variant handler with:
      """yaml
      code: "GENERIC_SWISS"
      productCode: "GENERIC_001"
      enabled: true
      supplierVariantName: "Generic Drug 10mg"
      supplierVariantCode: "CH_GEN_001"
      prescriptionRequired: true
      maximumQuantityPerOrder: 30
      supplierCode: "swiss-pharma"
      preferredSupplier: false
      costPrice:
        amount: 2750
        currency: CHF
      productOptions: {}
      translations:
        - locale: nl
          name: "Generiek Medicijn 10mg"
      channels:
        - dok_nl
      channelPricings:
        - channelCode: dok_nl
          enabled: true
          price:
            amount: 3500
            currency: EUR
      leaflets: []
      quantityMultiplier: 1
      """
    Then the product variant with code "GENERIC_SWISS" exists with product code "GENERIC_001"
    # CHF 27.50 * 0.92 = €25.30 = 2530 cents (accurate cost for pharmacy distribution)
    # Before fix: would be ceiling rounded to €26.00 = 2600 cents (€0.70 artificial inflation per unit)
    And the product variant has property "costPrice" with value 2530

  Scenario: Canadian supplier demonstrates cost accuracy for low-value medications
    Given the product variant with code "GENERIC_CANADIAN" does not exist
    When I execute the upsert product variant handler with:
      """yaml
      code: "GENERIC_CANADIAN"
      productCode: "GENERIC_001"
      enabled: true
      supplierVariantName: "Generic Drug 5mg"
      supplierVariantCode: "CA_GEN_001"
      prescriptionRequired: true
      maximumQuantityPerOrder: 60
      supplierCode: "canadian-pharma"
      preferredSupplier: false
      costPrice:
        amount: 67
        currency: CAD
      productOptions: {}
      translations:
        - locale: nl
          name: "Generiek Medicijn 5mg"
      channels:
        - dok_nl
      channelPricings:
        - channelCode: dok_nl
          enabled: true
          price:
            amount: 150
            currency: EUR
      leaflets: []
      quantityMultiplier: 1
      """
    Then the product variant with code "GENERIC_CANADIAN" exists with product code "GENERIC_001"
    # CAD $0.67 * 0.67 = €0.4489 = 45 cents (accurate cost)
    # Before fix: would be ceiling rounded to €1.00 = 100 cents (122% cost inflation!)
    # This demonstrates the severe impact on low-cost medications
    And the product variant has property "costPrice" with value 45

  Scenario: Dutch supplier baseline - no conversion needed for EUR
    Given the product variant with code "GENERIC_DUTCH" does not exist
    When I execute the upsert product variant handler with:
      """yaml
      code: "GENERIC_DUTCH"
      productCode: "GENERIC_001"
      enabled: true
      supplierVariantName: "Generic Drug 20mg"
      supplierVariantCode: "NL_GEN_001"
      prescriptionRequired: true
      maximumQuantityPerOrder: 30
      supplierCode: "dutch-pharma"
      preferredSupplier: true
      costPrice:
        amount: 1250
        currency: EUR
      productOptions: {}
      translations:
        - locale: nl
          name: "Generiek Medicijn 20mg"
      channels:
        - dok_nl
      channelPricings:
        - channelCode: dok_nl
          enabled: true
          price:
            amount: 2000
            currency: EUR
      leaflets: []
      quantityMultiplier: 1
      """
    Then the product variant with code "GENERIC_DUTCH" exists with product code "GENERIC_001"
    # €12.50 EUR remains exactly €12.50 EUR (no conversion needed)
    And the product variant has property "costPrice" with value 1250

  Scenario: Comparison of cost accuracy impact across suppliers
    # This scenario demonstrates the business impact of accurate cost conversion
    # by comparing the same medication from different suppliers
    
    # High-cost Swiss supplier
    Given the product variant with code "COMPARISON_SWISS" does not exist
    When I execute the upsert product variant handler with:
      """yaml
      code: "COMPARISON_SWISS"
      productCode: "GENERIC_001"
      enabled: true
      supplierVariantName: "Premium Generic 15mg"
      supplierVariantCode: "CH_PREM_001"
      prescriptionRequired: true
      maximumQuantityPerOrder: 20
      supplierCode: "swiss-pharma"
      preferredSupplier: false
      costPrice:
        amount: 543
        currency: CHF
      productOptions: {}
      translations: []
      channels: []
      channelPricings: []
      leaflets: []
      quantityMultiplier: 1
      """
    Then the product variant with code "COMPARISON_SWISS" exists with product code "GENERIC_001"
    # CHF 5.43 * 0.92 = €4.9956 = 500 cents (accurate)
    And the product variant has property "costPrice" with value 500

    # Low-cost Canadian supplier  
    Given the product variant with code "COMPARISON_CANADIAN" does not exist
    When I execute the upsert product variant handler with:
      """yaml
      code: "COMPARISON_CANADIAN"
      productCode: "GENERIC_001"
      enabled: true
      supplierVariantName: "Budget Generic 15mg"
      supplierVariantCode: "CA_BUDG_001"
      prescriptionRequired: true
      maximumQuantityPerOrder: 50
      supplierCode: "canadian-pharma"
      preferredSupplier: false
      costPrice:
        amount: 298
        currency: CAD
      productOptions: {}
      translations: []
      channels: []
      channelPricings: []
      leaflets: []
      quantityMultiplier: 1
      """
    Then the product variant with code "COMPARISON_CANADIAN" exists with product code "GENERIC_001"
    # CAD $2.98 * 0.67 = €1.9966 = 200 cents (accurate)
    And the product variant has property "costPrice" with value 200

    # This comparison shows:
    # - Swiss supplier: €5.00 (accurate cost)
    # - Canadian supplier: €2.00 (accurate cost)
    # - Clear cost difference for pharmacy distribution decisions
    # 
    # Before the fix, both would have been ceiling rounded, potentially to:
    # - Swiss: €6.00 (20% inflation)
    # - Canadian: €3.00 (50% inflation)
    # This would have distorted the true cost comparison for pharmacy distribution
